<?php

namespace App\Filament\Resources\ScholarshipPrograms\Schemas;

use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Schema;

class ScholarshipProgramInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                // Program Identity
                TextEntry::make('name')
                    ->label('Scholarship Name')
                    ->size('lg')
                    ->weight('bold'),

                TextEntry::make('description')
                    ->label('Description')
                    ->columnSpanFull(),

                TextEntry::make('active')
                    ->label('Status')
                    ->badge()
                    ->color(fn (bool $state): string => $state ? 'success' : 'gray')
                    ->formatStateUsing(fn (bool $state): string => $state ? 'Active' : 'Inactive'),

                // Academic Information
                TextEntry::make('semester')
                    ->label('Semester')
                    ->icon('heroicon-o-calendar'),

                TextEntry::make('academic_year')
                    ->label('Academic Year')
                    ->icon('heroicon-o-calendar-days'),

                TextEntry::make('application_deadline')
                    ->label('Application Deadline')
                    ->date('M j, Y')
                    ->icon('heroicon-o-clock')
                    ->color(fn ($record) => $record->application_deadline->isPast() ? 'danger' : 'gray'),

                // Budget Information
                TextEntry::make('total_budget')
                    ->label('Total Budget')
                    ->money('PHP', divideBy: 1)
                    ->icon('heroicon-o-banknotes'),

                TextEntry::make('per_student_budget')
                    ->label('Award per Student')
                    ->money('PHP', divideBy: 1)
                    ->icon('heroicon-o-currency-dollar'),

                TextEntry::make('available_slots')
                    ->label('Available Slots')
                    ->numeric()
                    ->icon('heroicon-o-users'),

                // Eligibility Requirements
                TextEntry::make('school_type_eligibility')
                    ->label('School Type Eligibility')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'high_school' => 'info',
                        'college' => 'success',
                        'both' => 'warning',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'high_school' => 'High School Students',
                        'college' => 'College Students',
                        'both' => 'All Students',
                        default => ucfirst($state),
                    }),

                TextEntry::make('min_gpa')
                    ->label('Minimum GPA')
                    ->numeric(decimalPlaces: 2)
                    ->suffix('%')
                    ->icon('heroicon-o-star'),

                TextEntry::make('min_units')
                    ->label('Minimum Units (College)')
                    ->numeric()
                    ->placeholder('Not applicable')
                    ->icon('heroicon-o-book-open')
                    ->visible(fn ($record) => $record->min_units !== null),

                TextEntry::make('community_service_days')
                    ->label('Required Community Service Days')
                    ->numeric()
                    ->icon('heroicon-o-heart'),

                // System Information
                TextEntry::make('scholarship_applications_count')
                    ->label('Total Applications')
                    ->counts('scholarshipApplications')
                    ->badge()
                    ->color('info')
                    ->icon('heroicon-o-document-text'),

                TextEntry::make('created_at')
                    ->label('Created')
                    ->dateTime('M j, Y g:i A')
                    ->icon('heroicon-o-calendar'),

                TextEntry::make('updated_at')
                    ->label('Last Updated')
                    ->dateTime('M j, Y g:i A')
                    ->icon('heroicon-o-pencil'),
            ]);
    }
}
