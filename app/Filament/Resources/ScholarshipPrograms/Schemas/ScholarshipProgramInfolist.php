<?php

namespace App\Filament\Resources\ScholarshipPrograms\Schemas;

use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Schemas\Schema;

class ScholarshipProgramInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                // Program Header - Main Information
                TextEntry::make('name')
                    ->label('')
                    ->size('xl')
                    ->weight('bold')
                    ->color('primary')
                    ->columnSpanFull(),

                TextEntry::make('description')
                    ->label('')
                    ->prose()
                    ->markdown()
                    ->columnSpanFull()
                    ->color('gray'),

                // Status and Key Metrics Row
                TextEntry::make('active')
                    ->label('Status')
                    ->badge()
                    ->size('lg')
                    ->color(fn (bool $state): string => $state ? 'success' : 'danger')
                    ->formatStateUsing(fn (bool $state): string => $state ? '🟢 Active' : '🔴 Inactive')
                    ->weight('bold'),

                TextEntry::make('application_deadline')
                    ->label('Application Deadline')
                    ->date('F j, Y')
                    ->size('lg')
                    ->weight('medium')
                    ->color(fn ($record) => $record->application_deadline->isPast() ? 'danger' : 'success')
                    ->icon('heroicon-o-calendar-days')
                    ->iconColor(fn ($record) => $record->application_deadline->isPast() ? 'danger' : 'success'),

                TextEntry::make('scholarship_applications_count')
                    ->label('Total Applications')
                    ->counts('scholarshipApplications')
                    ->badge()
                    ->size('lg')
                    ->color('info')
                    ->icon('heroicon-o-document-text')
                    ->suffix(' applications'),

                // Financial Information Section
                TextEntry::make('total_budget')
                    ->label('💰 Total Program Budget')
                    ->formatStateUsing(fn ($state) => '₱' . number_format($state, 2))
                    ->size('lg')
                    ->weight('bold')
                    ->color('success')
                    ->copyable()
                    ->copyMessage('Budget amount copied!')
                    ->helperText('Total funding allocated for this program'),

                TextEntry::make('per_student_budget')
                    ->label('💵 Award per Student')
                    ->formatStateUsing(fn ($state) => '₱' . number_format($state, 2))
                    ->size('lg')
                    ->weight('bold')
                    ->color('success')
                    ->copyable()
                    ->copyMessage('Award amount copied!')
                    ->helperText('Amount each student will receive'),

                TextEntry::make('available_slots')
                    ->label('👥 Available Slots')
                    ->numeric()
                    ->size('lg')
                    ->weight('bold')
                    ->color('warning')
                    ->suffix(' students')
                    ->helperText('Number of scholarships available'),

                // Academic Requirements Section
                TextEntry::make('semester')
                    ->label('📅 Semester')
                    ->badge()
                    ->color('info')
                    ->size('md'),

                TextEntry::make('academic_year')
                    ->label('🗓️ Academic Year')
                    ->badge()
                    ->color('info')
                    ->size('md'),

                TextEntry::make('school_type_eligibility')
                    ->label('🎓 Eligible Students')
                    ->badge()
                    ->size('md')
                    ->color(fn (string $state): string => match ($state) {
                        'high_school' => 'blue',
                        'college' => 'green',
                        'both' => 'purple',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'high_school' => '🏫 High School Students',
                        'college' => '🏛️ College Students',
                        'both' => '🎯 All Students',
                        default => ucfirst($state),
                    }),

                // Performance Requirements
                TextEntry::make('min_gpa')
                    ->label('📊 Minimum GPA Required')
                    ->formatStateUsing(fn ($state) => '⭐ ' . number_format($state, 2) . '%')
                    ->badge()
                    ->color('yellow')
                    ->size('md')
                    ->helperText('Students must maintain this GPA or higher'),

                TextEntry::make('min_units')
                    ->label('📚 Minimum Units (College)')
                    ->formatStateUsing(fn ($state) => '📖 ' . $state . ' units')
                    ->badge()
                    ->color('blue')
                    ->size('md')
                    ->visible(fn ($record) => $record->min_units !== null)
                    ->helperText('Required course load for college students'),

                TextEntry::make('community_service_days')
                    ->label('🤝 Community Service Requirement')
                    ->formatStateUsing(fn ($state) => '❤️ ' . $state . ' days required')
                    ->badge()
                    ->color('pink')
                    ->size('md')
                    ->helperText('Students must complete this many days of community service'),

                // Document Requirements
                RepeatableEntry::make('documentRequirements')
                    ->label('📋 Document Requirements')
                    ->schema([
                        TextEntry::make('name')
                            ->label('')
                            ->weight('semibold')
                            ->size('md')
                            ->formatStateUsing(fn ($state) => '📄 ' . $state)
                            ->color('primary'),

                        TextEntry::make('is_required')
                            ->label('')
                            ->badge()
                            ->color(fn (bool $state): string => $state ? 'danger' : 'warning')
                            ->formatStateUsing(fn (bool $state): string => $state ? '🔴 Required' : '⚪ Optional'),

                        TextEntry::make('description')
                            ->label('')
                            ->color('gray')
                            ->italic()
                            ->formatStateUsing(fn ($state) => '💡 ' . $state)
                            ->columnSpanFull(),
                    ])
                    ->columns(2)
                    ->contained(false)
                    ->visible(fn ($record) => $record->documentRequirements && $record->documentRequirements->count() > 0)
                    ->columnSpanFull(),

                // System Information (Less Prominent)
                TextEntry::make('created_at')
                    ->label('📅 Created')
                    ->dateTime('F j, Y \a\t g:i A')
                    ->color('gray')
                    ->size('sm')
                    ->icon('heroicon-o-plus-circle'),

                TextEntry::make('updated_at')
                    ->label('✏️ Last Updated')
                    ->dateTime('F j, Y \a\t g:i A')
                    ->color('gray')
                    ->size('sm')
                    ->icon('heroicon-o-pencil-square'),
            ])
            ->columns(3); // Use 3-column layout for better organization
    }
}
