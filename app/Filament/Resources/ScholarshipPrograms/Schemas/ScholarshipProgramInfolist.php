<?php

namespace App\Filament\Resources\ScholarshipPrograms\Schemas;

use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Schema;

class ScholarshipProgramInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                // Program Header
                TextEntry::make('name')
                    ->label('Scholarship Name')
                    ->size('lg')
                    ->weight('bold')
                    ->columnSpanFull(),

                TextEntry::make('description')
                    ->label('Description')
                    ->columnSpanFull(),

                // Budget Information
                TextEntry::make('total_budget')
                    ->label('Total Budget')
                    ->formatStateUsing(fn ($state) => '₱' . number_format($state, 2))
                    ->icon('heroicon-o-banknotes'),

                TextEntry::make('per_student_budget')
                    ->label('Per Student Budget')
                    ->formatStateUsing(fn ($state) => '₱' . number_format($state, 2))
                    ->icon('heroicon-o-currency-dollar'),

                TextEntry::make('available_slots')
                    ->label('Available Slots')
                    ->numeric()
                    ->icon('heroicon-o-users'),

                TextEntry::make('scholarship_applications_count')
                    ->label('Applications')
                    ->counts('scholarshipApplications')
                    ->badge()
                    ->color('info')
                    ->icon('heroicon-o-document-text'),

                TextEntry::make('application_deadline')
                    ->label('Deadline')
                    ->date('M j, Y')
                    ->icon('heroicon-o-clock')
                    ->color(fn ($record) => $record->application_deadline->isPast() ? 'danger' : 'gray'),

                TextEntry::make('active')
                    ->label('Status')
                    ->badge()
                    ->color(fn (bool $state): string => $state ? 'success' : 'gray')
                    ->formatStateUsing(fn (bool $state): string => $state ? 'Active' : 'Inactive')
                    ->icon(fn (bool $state): string => $state ? 'heroicon-o-check-circle' : 'heroicon-o-x-circle'),

                // Eligibility Criteria
                TextEntry::make('school_type_eligibility')
                    ->label('School Type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'high_school' => 'info',
                        'college' => 'success',
                        'both' => 'warning',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'high_school' => 'High School',
                        'college' => 'College',
                        'both' => 'All Students',
                        default => ucfirst($state),
                    }),

                TextEntry::make('min_gpa')
                    ->label('Min. GPA')
                    ->formatStateUsing(fn ($state) => number_format($state, 2) . '%')
                    ->badge()
                    ->color('gray'),

                TextEntry::make('min_units')
                    ->label('Min. Units')
                    ->numeric()
                    ->badge()
                    ->color('gray')
                    ->visible(fn ($record) => $record->min_units !== null),

                TextEntry::make('semester')
                    ->label('Semester')
                    ->badge()
                    ->color('gray'),

                TextEntry::make('academic_year')
                    ->label('Academic Year')
                    ->badge()
                    ->color('gray'),

                TextEntry::make('community_service_days')
                    ->label('Community Service')
                    ->formatStateUsing(fn ($state) => $state . ' days')
                    ->badge()
                    ->color('gray'),

                // System Information
                TextEntry::make('created_at')
                    ->label('Created')
                    ->dateTime('M j, Y g:i A')
                    ->icon('heroicon-o-calendar'),

                TextEntry::make('updated_at')
                    ->label('Last Updated')
                    ->dateTime('M j, Y g:i A')
                    ->icon('heroicon-o-pencil'),
            ]);
    }
}
