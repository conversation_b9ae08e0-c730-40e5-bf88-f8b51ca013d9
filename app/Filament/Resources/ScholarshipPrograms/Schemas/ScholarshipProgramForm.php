<?php

namespace App\Filament\Resources\ScholarshipPrograms\Schemas;

use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Select;
use Filament\Schemas\Schema;

class ScholarshipProgramForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                // General Information
                TextInput::make('name')
                    ->label('Scholarship Name')
                    ->required()
                    ->maxLength(255)
                    ->placeholder('Enter scholarship program name'),

                Textarea::make('description')
                    ->label('Description')
                    ->required()
                    ->rows(4)
                    ->placeholder('Describe the scholarship program, its purpose, and goals')
                    ->columnSpanFull(),

                Select::make('semester')
                    ->label('Semester')
                    ->required()
                    ->options([
                        '1st Semester' => '1st Semester',
                        '2nd Semester' => '2nd Semester',
                        'Summer Term' => 'Summer Term',
                        'Annual' => 'Annual',
                    ])
                    ->native(false),

                TextInput::make('academic_year')
                    ->label('Academic Year')
                    ->required()
                    ->placeholder('YYYY-YYYY (e.g., 2024-2025)')
                    ->maxLength(9),

                DatePicker::make('application_deadline')
                    ->label('Application Deadline')
                    ->required()
                    ->native(false)
                    ->displayFormat('M j, Y'),

                Toggle::make('active')
                    ->label('Scholarship is Active')
                    ->default(true),

                // Budget & Eligibility
                TextInput::make('total_budget')
                    ->label('Total Budget (₱)')
                    ->required()
                    ->numeric()
                    ->minValue(0)
                    ->step(0.01)
                    ->placeholder('0.00'),

                TextInput::make('per_student_budget')
                    ->label('Award per Student (₱)')
                    ->required()
                    ->numeric()
                    ->minValue(0)
                    ->step(0.01)
                    ->placeholder('0.00'),

                TextInput::make('available_slots')
                    ->label('Available Slots')
                    ->required()
                    ->numeric()
                    ->minValue(1)
                    ->placeholder('Number of scholarship slots'),

                Select::make('school_type_eligibility')
                    ->label('School Type Eligibility')
                    ->required()
                    ->options([
                        'high_school' => 'High School Students',
                        'college' => 'College Students',
                        'both' => 'Both High School and College',
                    ])
                    ->native(false),

                TextInput::make('min_gpa')
                    ->label('Minimum GPA (0-100%)')
                    ->required()
                    ->numeric()
                    ->minValue(0)
                    ->maxValue(100)
                    ->step(0.01)
                    ->placeholder('75.00'),

                TextInput::make('min_units')
                    ->label('Minimum Units (College Only)')
                    ->numeric()
                    ->minValue(0)
                    ->placeholder('12')
                    ->helperText('Leave empty for high school or if not applicable')
                    ->disabled(fn (callable $get) => $get('school_type_eligibility') === 'high_school')
                    ->dehydrated(fn (callable $get) => $get('school_type_eligibility') !== 'high_school'),

                TextInput::make('community_service_days')
                    ->label('Required Community Service Days')
                    ->required()
                    ->numeric()
                    ->minValue(0)
                    ->default(6)
                    ->placeholder('6'),
            ]);
    }
}
